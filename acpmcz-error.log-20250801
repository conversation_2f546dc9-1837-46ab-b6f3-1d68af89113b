2025/07/31 00:06:56 [error] 248968#248968: *1464405 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.git/index HTTP/1.1", host: "*************"
2025/07/31 00:08:47 [error] 248969#248969: *1464563 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 00:48:12 [error] 248969#248969: *1468077 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 00:52:30 [error] 248969#248969: *1468438 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 01:45:26 [error] 248968#248968: *1473146 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 01:55:42 [error] 248969#248969: *1474102 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.git/config HTTP/1.1", host: "*************"
2025/07/31 02:13:14 [error] 248969#248969: *1475663 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 02:14:53 [error] 248968#248968: *1475789 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 02:19:38 [error] 248968#248968: *1476201 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "http://www.acpm.cz/sitemap.xml"
2025/07/31 02:45:00 [error] 248968#248968: *1479359 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 03:07:43 [error] 248969#248969: *1481400 open() "/home/<USER>/public_html/www/+CSCOE+/logon.html" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /+CSCOE+/logon.html HTTP/1.1", host: "*************:443"
2025/07/31 03:19:52 [error] 248968#248968: *1482536 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 03:34:18 [error] 248969#248969: *1483758 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 04:09:47 [error] 255450#255450: *1487092 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 04:44:37 [error] 255451#255451: *1490201 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 04:47:55 [error] 255451#255451: *1490490 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "http://www.acpm.cz/sitemap.xml"
2025/07/31 05:13:24 [error] 255450#255450: *1493374 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "http://www.acpm.cz/sitemap.xml"
2025/07/31 05:16:33 [error] 255450#255450: *1493903 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 05:44:58 [error] 255451#255451: *1498658 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 06:07:57 [error] 255451#255451: *1502906 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 06:19:19 [error] 255450#255450: *1505024 open() "/home/<USER>/public_html/www/localizations/zh.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /localizations/zh.json HTTP/1.1", host: "*************"
2025/07/31 06:54:00 [error] 255451#255451: *1513920 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.well-known/traffic-advice HTTP/1.1", host: "www.acpm.cz"
2025/07/31 06:54:17 [error] 255451#255451: *1514012 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 07:08:47 [error] 255450#255450: *1518402 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 07:24:25 [error] 255450#255450: *1523580 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 08:19:25 [error] 255451#255451: *1531325 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 08:36:10 [error] 255450#255450: *1533314 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 08:55:07 [error] 255451#255451: *1535612 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.well-known/traffic-advice HTTP/1.1", host: "www.acpm.cz"
2025/07/31 09:16:28 [error] 255450#255450: *1538203 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 09:28:11 [error] 255450#255450: *1540548 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "*************"
2025/07/31 09:49:30 [error] 255450#255450: *1546380 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 10:09:46 [error] 255450#255450: *1551650 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "*************"
2025/07/31 10:15:02 [error] 255450#255450: *1553009 open() "/home/<USER>/public_html/www/webpages/login.html" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET //webpages/login.html HTTP/1.1", host: "*************"
2025/07/31 10:21:22 [error] 255451#255451: *1554644 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 10:58:42 [error] 255451#255451: *1564091 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 11:00:40 [error] 255450#255450: *1564609 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.well-known/traffic-advice HTTP/1.1", host: "www.acpm.cz"
2025/07/31 11:14:56 [error] 255451#255451: *1568342 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 11:46:54 [error] 255450#255450: *1576832 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.aws/credentials HTTP/1.1", host: "*************"
2025/07/31 12:01:56 [error] 255450#255450: *1579391 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 12:22:22 [error] 255451#255451: *1584012 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 12:31:57 [error] 255450#255450: *1586176 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.well-known/traffic-advice HTTP/1.1", host: "www.acpm.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /apps/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /blog/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.production HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /protected/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /config/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /base/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.development HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /v2/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /database/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.bak HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/package.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /package.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /conf/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:48 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.AWS_/credentials HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/karma.conf.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /karma.conf.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /app/config/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /backend/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.save HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /crm/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /laravel/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /system/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.htaccess HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/config/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /app/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /admin/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:53 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /cgi-bin/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:58 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /server/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:58 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /old/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:58 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:40:58 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /resources/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:03 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /wp-content/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:03 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:03 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.backup HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:03 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:03 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /library/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:08 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /v1/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:08 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /wp-admin/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:08 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.testing HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:13 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.old HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:13 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /public/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:13 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.aws/credentials HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:18 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.local HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:18 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /core/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:19 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /storage/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/index.html" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /index.html HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /settings/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /src/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /portal/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /env/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /dev/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.local HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.production HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:24 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.staging HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:29 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /awstats/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:30 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /cron/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:30 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /www/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:30 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /docker/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:30 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /docker/app/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:30 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.vscode/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:30 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /js/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:30 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /laravel/core/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /mail/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /mailer/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /nginx/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /site/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /xampp/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /main/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /node_modules/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /kyc/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /prod/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /website/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /development/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/shared/config/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:35 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/shared/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:36 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /node/.env_example HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:36 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.production.local HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:36 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.example HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:36 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.stage HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:41 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env_sample HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:41 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.prod HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:41 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /local/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:41 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /application/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:41 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /web/.env HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:48 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/appsettings.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /appsettings.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:41:48 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:42:00 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/swagger.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /swagger.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:42:16 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/configs/s3_config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /configs/s3_config.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:42:32 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/config/settings.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config/settings.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:42:32 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.circleci/configs/development.yml HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:42:37 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.travis.yml HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:42:37 [error] 255450#255450: *1588098 open() "/home/<USER>/public_html/www/config/config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config/config.json HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:42:43 [error] 255450#255450: *1588098 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.aws/config HTTP/1.1", host: "mail.praguecityline.cz"
2025/07/31 12:43:34 [error] 255450#255450: *1588772 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.git/config HTTP/1.1", host: "*************"
2025/07/31 13:02:24 [error] 255451#255451: *1593216 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 13:14:07 [error] 255450#255450: *1595887 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "*************"
2025/07/31 13:18:37 [error] 255450#255450: *1597207 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "*************"
2025/07/31 13:50:20 [error] 255451#255451: *1603930 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.well-known/traffic-advice HTTP/1.1", host: "www.acpm.cz"
2025/07/31 14:18:03 [error] 255450#255450: *1611487 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 14:57:22 [error] 255450#255450: *1625549 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "http://www.acpm.cz/sitemap.xml"
2025/07/31 15:04:02 [error] 255450#255450: *1628492 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.well-known/traffic-advice HTTP/1.1", host: "www.acpm.cz"
2025/07/31 15:22:48 [error] 255450#255450: *1637545 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "http://www.acpm.cz/sitemap.xml"
2025/07/31 15:24:07 [error] 255451#255451: *1638486 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 15:48:49 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /apps/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:49 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:49 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /blog/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:49 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.production HTTP/1.1", host: "*************"
2025/07/31 15:48:49 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /protected/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:49 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /config/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /base/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.development HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /v2/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /database/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.bak HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/package.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /package.json HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /conf/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.AWS_/credentials HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/karma.conf.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /karma.conf.json HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /app/config/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /backend/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.save HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /crm/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /laravel/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /system/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.htaccess HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/config/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /app/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /admin/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /cgi-bin/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /server/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:52 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /old/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:55 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.json HTTP/1.1", host: "*************"
2025/07/31 15:48:55 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /resources/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:55 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /wp-content/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:55 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.json HTTP/1.1", host: "*************"
2025/07/31 15:48:55 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.backup HTTP/1.1", host: "*************"
2025/07/31 15:48:58 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "*************"
2025/07/31 15:48:58 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /library/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:58 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /v1/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:58 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /wp-admin/.env HTTP/1.1", host: "*************"
2025/07/31 15:48:58 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.testing HTTP/1.1", host: "*************"
2025/07/31 15:48:58 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.old HTTP/1.1", host: "*************"
2025/07/31 15:49:01 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /public/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:01 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.aws/credentials HTTP/1.1", host: "*************"
2025/07/31 15:49:01 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.local HTTP/1.1", host: "*************"
2025/07/31 15:49:01 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /core/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:01 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /storage/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:02 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/index.html" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /index.html HTTP/1.1", host: "*************"
2025/07/31 15:49:02 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /settings/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /src/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /portal/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /env/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /dev/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.local HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.production HTTP/1.1", host: "*************"
2025/07/31 15:49:04 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.staging HTTP/1.1", host: "*************"
2025/07/31 15:49:07 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /awstats/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:07 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /cron/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:07 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /www/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:07 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /docker/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:07 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /docker/app/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:07 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.vscode/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:07 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /js/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /laravel/core/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /mail/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /mailer/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /nginx/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /site/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /xampp/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /main/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:09 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /node_modules/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:10 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /kyc/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:10 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /prod/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:10 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /website/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:10 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /development/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:10 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/shared/config/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/shared/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /node/.env_example HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.production.local HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.example HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.stage HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env_sample HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.prod HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /local/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /application/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:13 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /web/.env HTTP/1.1", host: "*************"
2025/07/31 15:49:19 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/appsettings.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /appsettings.json HTTP/1.1", host: "*************"
2025/07/31 15:49:19 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config.json HTTP/1.1", host: "*************"
2025/07/31 15:49:22 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/swagger.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /swagger.json HTTP/1.1", host: "*************"
2025/07/31 15:49:28 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/configs/s3_config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /configs/s3_config.json HTTP/1.1", host: "*************"
2025/07/31 15:49:31 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/config/settings.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config/settings.json HTTP/1.1", host: "*************"
2025/07/31 15:49:31 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.circleci/configs/development.yml HTTP/1.1", host: "*************"
2025/07/31 15:49:35 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.travis.yml HTTP/1.1", host: "*************"
2025/07/31 15:49:35 [error] 255451#255451: *1653030 open() "/home/<USER>/public_html/www/config/config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config/config.json HTTP/1.1", host: "*************"
2025/07/31 15:49:35 [error] 255451#255451: *1653030 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.aws/config HTTP/1.1", host: "*************"
2025/07/31 16:12:24 [error] 255451#255451: *1662140 access forbidden by rule, client: ************, server: www.acpm.cz, request: "GET /.well-known/traffic-advice HTTP/1.1", host: "www.acpm.cz"
2025/07/31 16:15:12 [error] 255450#255450: *1663281 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "http://www.acpm.cz/sitemap.xml"
2025/07/31 16:18:47 [error] 255451#255451: *1664529 open() "/home/<USER>/public_html/www/sitemaps.xml" failed (2: No such file or directory), client: ************, server: www.acpm.cz, request: "GET /sitemaps.xml HTTP/2.0", host: "sitemap.acpm.cz"
2025/07/31 16:43:05 [error] 255451#255451: *1673334 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.pocasi.praguecityline.cz"
2025/07/31 16:44:10 [error] 255450#255450: *1673681 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 17:15:35 [error] 255450#255450: *1680725 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 17:20:32 [error] 255451#255451: *1681494 open() "/home/<USER>/public_html/www/sitemaps.xml" failed (2: No such file or directory), client: ************, server: www.acpm.cz, request: "GET /sitemaps.xml HTTP/2.0", host: "sitemap.acpm.cz"
2025/07/31 17:44:42 [error] 255450#255450: *1685175 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: ************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.pocasi.praguecityline.cz"
2025/07/31 17:59:23 [error] 255450#255450: *1687593 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 19:30:05 [error] 255451#255451: *1701948 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 20:02:19 [error] 255451#255451: *1706550 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 20:17:18 [error] 255451#255451: *1708552 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 20:40:15 [error] 255450#255450: *1711540 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 20:53:11 [error] 255450#255450: *1713157 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /app/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env.bak HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env.example HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env.local HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env.old HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env.prod HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env.production.local HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.env.stage HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /admin/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /api/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /apps/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.git/config HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/api/config/tsconfig.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /api/config/tsconfig.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /products/.gitlab-ci.yml HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.aws/credentials HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/appsettings.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /appsettings.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:15 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/index.html" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /index.html HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:16 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/config/test.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /config/test.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:16 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/config/config.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /config/config.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:16 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.profile HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:16 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/settings.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /settings.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/config/aws.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /config/aws.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/manifest.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /manifest.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/config.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /config.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 access forbidden by rule, client: *************, server: www.acpm.cz, request: "GET /.aws/config HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/appsettings.Development.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /appsettings.Development.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/config/default.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /config/default.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/config/production.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /config/production.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:05:17 [error] 255451#255451: *1714741 open() "/home/<USER>/public_html/www/appsettings.Production.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /appsettings.Production.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:11 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /apps/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:11 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:11 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /blog/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:11 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.production HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /protected/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /config/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /base/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.development HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /v2/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /database/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.bak HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/package.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /package.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /conf/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:15 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.AWS_/credentials HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/karma.conf.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /karma.conf.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /app/config/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /backend/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.save HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /crm/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /laravel/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /system/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.htaccess HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:19 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/config/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:23 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /app/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:23 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /admin/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:23 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /cgi-bin/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:23 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /server/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:23 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /old/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:26 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:26 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /resources/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:26 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /wp-content/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.backup HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /library/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /v1/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /wp-admin/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.testing HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.old HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /public/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.aws/credentials HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.local HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:30 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /core/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:34 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /storage/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:34 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/index.html" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /index.html HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:34 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /settings/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:34 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /src/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:34 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:37 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /portal/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:37 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /env/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:37 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /dev/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:37 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.local HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:37 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.production HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:37 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /new/.env.staging HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /awstats/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /cron/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /www/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /docker/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /docker/app/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.vscode/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /js/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /laravel/core/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /mail/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /mailer/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /nginx/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /site/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /xampp/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /main/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /node_modules/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /kyc/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:38 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /prod/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:41 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /website/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:41 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /development/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:41 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/shared/config/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:41 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /api/shared/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /node/.env_example HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.production.local HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.example HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.stage HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env_sample HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.env.prod HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /local/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /application/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:42 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /web/.env HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:48 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/appsettings.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /appsettings.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:48 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:53:52 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/swagger.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /swagger.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:54:00 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/configs/s3_config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /configs/s3_config.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:54:04 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/config/settings.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config/settings.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:54:04 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.circleci/configs/development.yml HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:54:08 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.travis.yml HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:54:08 [error] 255450#255450: *1720889 open() "/home/<USER>/public_html/www/config/config.json" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /config/config.json HTTP/1.1", host: "www.acpm.cz"
2025/07/31 21:54:11 [error] 255450#255450: *1720889 access forbidden by rule, client: **************, server: www.acpm.cz, request: "GET /.aws/config HTTP/1.1", host: "www.acpm.cz"
2025/07/31 22:08:42 [error] 255451#255451: *1722868 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "https://acpm.cz/sitemap.xml"
2025/07/31 22:30:56 [error] 255451#255451: *1725644 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 22:31:02 [error] 255451#255451: *1725644 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz", referrer: "http://www.acpm.cz/sitemap.xml"
2025/07/31 23:22:35 [error] 255450#255450: *1731805 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: **************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/2.0", host: "www.acpm.cz"
2025/07/31 23:38:57 [error] 255450#255450: *1733862 open() "/home/<USER>/public_html/www/sitemap.xml" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /sitemap.xml HTTP/1.1", host: "*************"
2025/07/31 23:39:07 [error] 255450#255450: *1733911 open() "/home/<USER>/public_html/www/config.json" failed (2: No such file or directory), client: *************, server: www.acpm.cz, request: "GET /config.json HTTP/1.1", host: "*************"
